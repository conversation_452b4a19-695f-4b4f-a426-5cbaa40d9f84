dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.026639756101828355
  max_inference_time_ms: 0.6678178906440735
  min_inference_time_ms: 0.00745430588722229
  std_inference_time_ms: 0.014869788850516492
model_complexity:
  macs: 93.737M
  macs_raw: 93737088.0
  parameters: 8.524M
  params_raw: 8523801.0
overall_metrics:
  accuracy: 39.29519230769231
  kappa: 0.36765825320512824
  macro_f1: 34.61265644139275
test_info:
  config_path: config.yaml
  model_path: ./saved_models/ic_amcnet/torchsig1024_20250709_224454/models/best_model.pth
  test_date: '2025-07-10 09:44:08'
