dataset_info:
  dataset_type: rml201801a
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 24
  snr_range:
  - -20.0
  - 30.0
  total_samples: 383386
inference_performance:
  avg_inference_time_ms: 0.02922570023899833
  max_inference_time_ms: 1.2553746883685772
  min_inference_time_ms: 0.007621943950653076
  std_inference_time_ms: 0.015536138911123853
model_complexity:
  macs: 93.737M
  macs_raw: 93736960.0
  parameters: 8.524M
  params_raw: 8523672.0
overall_metrics:
  accuracy: 43.35291325191843
  kappa: 0.4089000257947354
  macro_f1: 42.6682417733379
test_info:
  config_path: config.yaml
  model_path: ./saved_models/ic_amcnet/rml201801a_20250620_235945/models/best_model.pth
  test_date: '2025-06-21 22:43:36'
