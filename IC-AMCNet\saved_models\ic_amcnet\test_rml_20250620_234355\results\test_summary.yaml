dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 66000
inference_performance:
  avg_inference_time_ms: 0.009517322887073862
  max_inference_time_ms: 1.0074526071548462
  min_inference_time_ms: 0.0071302056312561035
  std_inference_time_ms: 0.01828887626374553
model_complexity:
  macs: 12.077M
  macs_raw: 12077440.0
  parameters: 1.182M
  params_raw: 1181963.0
overall_metrics:
  accuracy: 53.268181818181816
  kappa: 0.48595
  macro_f1: 53.937607601612214
test_info:
  config_path: config.yaml
  model_path: ./saved_models/ic_amcnet/rml_20250619_001221/models/best_model.pth
  test_date: '2025-06-20 23:44:26'
