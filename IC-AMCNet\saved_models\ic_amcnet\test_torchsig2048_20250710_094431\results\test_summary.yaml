dataset_info:
  dataset_type: torchsig2048
  input_shape: !!python/tuple
  - 2
  - 2048
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.030425633375461284
  max_inference_time_ms: 0.6554350256919861
  min_inference_time_ms: 0.007893890142440796
  std_inference_time_ms: 0.01569126337225935
model_complexity:
  macs: 187.060M
  macs_raw: 187060352.0
  parameters: 16.912M
  params_raw: 16912409.0
overall_metrics:
  accuracy: 43.67163461538462
  kappa: 0.4132461939102564
  macro_f1: 39.354646495109385
test_info:
  config_path: config.yaml
  model_path: ./saved_models/ic_amcnet/torchsig2048_20250709_230734/models/best_model.pth
  test_date: '2025-07-10 09:46:20'
